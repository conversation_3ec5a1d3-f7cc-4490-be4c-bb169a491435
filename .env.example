# Django Settings
SECRET_KEY=your-secret-key-here-generate-a-new-one
DEBUG=True
DJANGO_ENVIRONMENT=development
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Settings
DB_ENGINE=django.db.backends.mysql
DB_NAME=your_database_name
DB_USER=your_database_user
DB_PASSWORD=your_database_password
DB_HOST=localhost
DB_PORT=3306

# LLM API Keys (Configure only the ones you need)
XAI_API_KEY=your_xai_api_key_here
GROK_API_KEY=your_grok_api_key_here
GROQ_API_KEY=your_groq_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here
MISTRAL_API_KEY=your_mistral_api_key_here

# LLM Configuration
ACTIVE_LLM_PROVIDER=grok
ACTIVE_GEMINI_MODEL=gemini-1.5-pro

# Production-only settings (uncomment for production)
# REDIS_URL=redis://127.0.0.1:6379/1
# EMAIL_HOST=smtp.example.com
# EMAIL_PORT=587
# EMAIL_USE_TLS=True
# EMAIL_HOST_USER=your_email_user
# EMAIL_HOST_PASSWORD=your_email_password
# DEFAULT_FROM_EMAIL=<EMAIL>
# ADMIN_URL=secure-admin-path/
