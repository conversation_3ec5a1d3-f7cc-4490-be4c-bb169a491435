/**
 * Configuration module for the chat application
 * 
 * This module centralizes all configuration values including:
 * - API endpoints
 * - Application settings
 * - Feature flags
 * - Environment-specific configurations
 */

// API Configuration
export const API_CONFIG = {
  // Base API endpoints
  BASE_URL: window.location.origin,
  
  // Chat API endpoints
  CHAT: {
    API: '/chat/api/',
    MULTIMODAL: '/chat/api/multimodal/',
    SEGMENT: '/chat/api/segment/',
    MODELS: '/chat/api/models/',
    LOAD_MESSAGES: (chatId) => `/chat/load_chat_messages/${chatId}/`,
    DELETE_CHAT: (chatId) => `/chat/delete_chat/${chatId}/`,
    DELETE_ALL: '/chat/delete_all_chats/',
    UPDATE_TITLE: (chatId) => `/chat/update_chat_title/${chatId}/`,
  },
  
  // Request configuration
  REQUEST: {
    TIMEOUT: 30000, // 30 seconds
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000, // 1 second
  }
};

// Application Configuration
export const APP_CONFIG = {
  // File upload settings
  FILE_UPLOAD: {
    MAX_SIZE: 5 * 1024 * 1024, // 5MB
    ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    PREVIEW_SIZE: 200, // pixels
  },
  
  // Chat settings
  CHAT: {
    MAX_MESSAGE_LENGTH: 100000,
    AUTO_SCROLL_THRESHOLD: 100, // pixels from bottom
    TYPING_INDICATOR_DELAY: 500, // milliseconds
    MESSAGE_ANIMATION_DELAY: 100, // milliseconds
  },
  
  // UI settings
  UI: {
    SIDEBAR_ANIMATION_DURATION: 300, // milliseconds
    THEME_TRANSITION_DURATION: 300, // milliseconds
    NOTIFICATION_DURATION: 5000, // milliseconds
    DEBOUNCE_DELAY: 300, // milliseconds
  },
  
  // Memory management
  MEMORY: {
    MAX_CHAT_HISTORY: 50, // messages to keep in memory
    CLEANUP_INTERVAL: 300000, // 5 minutes
    MAX_EVENT_LISTENERS: 100,
  },
  
  // Feature flags
  FEATURES: {
    SPEECH_RECOGNITION: true,
    FILE_UPLOAD: true,
    SEGMENT_CHAT: true,
    NOTEBOOK: true,
    NOTES: true,
    EXAM_MODE: true,
    MATH_RENDERING: true,
    CODE_HIGHLIGHTING: true,
  }
};

// Environment Detection
export const ENVIRONMENT = {
  IS_DEVELOPMENT: window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1',
  IS_PRODUCTION: window.location.protocol === 'https:' && !window.location.hostname.includes('localhost'),
  IS_MOBILE: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
  IS_TOUCH_DEVICE: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
};

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK: 'Network error. Please check your connection and try again.',
  SERVER: 'Server error. Please try again later.',
  TIMEOUT: 'Request timed out. Please try again.',
  FILE_TOO_LARGE: 'File is too large. Maximum size is 5MB.',
  INVALID_FILE_TYPE: 'Invalid file type. Only images are allowed.',
  CHAT_LOAD_FAILED: 'Failed to load chat messages.',
  CHAT_DELETE_FAILED: 'Failed to delete chat.',
  API_KEY_MISSING: 'API configuration error. Please contact support.',
  GENERIC: 'An unexpected error occurred. Please try again.',
};

// Success Messages
export const SUCCESS_MESSAGES = {
  CHAT_DELETED: 'Chat deleted successfully.',
  ALL_CHATS_DELETED: 'All chats deleted successfully.',
  CHAT_RENAMED: 'Chat renamed successfully.',
  FILE_UPLOADED: 'File uploaded successfully.',
  SETTINGS_SAVED: 'Settings saved successfully.',
};

// Local Storage Keys
export const STORAGE_KEYS = {
  ACTIVE_CHAT_ID: 'activeChatId',
  THEME_PREFERENCE: 'preferred-theme',
  MODEL_PREFERENCE: 'preferred-model',
  PROVIDER_PREFERENCE: 'preferred-provider',
  UI_PREFERENCES: 'ui-preferences',
  CHAT_DRAFTS: 'chat-drafts',
};

// CSS Classes
export const CSS_CLASSES = {
  THEMES: {
    LIGHT: 'light-theme',
    DARK: 'dark-theme',
  },
  STATES: {
    ACTIVE: 'active',
    VISIBLE: 'visible',
    HIDDEN: 'hidden',
    LOADING: 'loading',
    ERROR: 'error',
    SUCCESS: 'success',
  },
  ANIMATIONS: {
    FADE_IN: 'fade-in',
    FADE_OUT: 'fade-out',
    SLIDE_IN: 'slide-in',
    SLIDE_OUT: 'slide-out',
  }
};

// Validation Rules
export const VALIDATION = {
  MESSAGE: {
    MIN_LENGTH: 1,
    MAX_LENGTH: APP_CONFIG.CHAT.MAX_MESSAGE_LENGTH,
  },
  CHAT_TITLE: {
    MIN_LENGTH: 1,
    MAX_LENGTH: 255,
  },
  FILE_NAME: {
    MAX_LENGTH: 255,
  }
};

// Export default configuration object
export default {
  API_CONFIG,
  APP_CONFIG,
  ENVIRONMENT,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  STORAGE_KEYS,
  CSS_CLASSES,
  VALIDATION,
};
