/**
 * Error handling module for the chat application
 * 
 * This module provides centralized error handling including:
 * - Network error handling
 * - API error processing
 * - User-friendly error messages
 * - Error logging and reporting
 * - Retry mechanisms
 */

import { ERROR_MESSAGES, ENVIRONMENT, APP_CONFIG } from './config.js';

// Error types
export const ERROR_TYPES = {
  NETWORK: 'network',
  API: 'api',
  VALIDATION: 'validation',
  FILE: 'file',
  AUTHENTICATION: 'authentication',
  PERMISSION: 'permission',
  TIMEOUT: 'timeout',
  UNKNOWN: 'unknown'
};

// Error severity levels
export const ERROR_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

/**
 * Main error handler class
 */
export class ErrorHandler {
  constructor() {
    this.errorQueue = [];
    this.maxQueueSize = 50;
    this.retryAttempts = new Map();
    
    // Initialize error reporting
    this.initializeErrorReporting();
  }

  /**
   * Initialize global error reporting
   */
  initializeErrorReporting() {
    // Global error handler for unhandled JavaScript errors
    window.addEventListener('error', (event) => {
      this.handleError({
        type: ERROR_TYPES.UNKNOWN,
        message: event.message,
        source: event.filename,
        line: event.lineno,
        column: event.colno,
        error: event.error
      });
    });

    // Global handler for unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError({
        type: ERROR_TYPES.UNKNOWN,
        message: 'Unhandled promise rejection',
        error: event.reason
      });
    });
  }

  /**
   * Main error handling method
   * @param {Object} errorInfo - Error information object
   * @param {string} errorInfo.type - Error type
   * @param {string} errorInfo.message - Error message
   * @param {Error} errorInfo.error - Original error object
   * @param {string} errorInfo.context - Additional context
   * @param {string} errorInfo.severity - Error severity
   */
  handleError(errorInfo) {
    const processedError = this.processError(errorInfo);
    
    // Log error
    this.logError(processedError);
    
    // Add to error queue
    this.addToQueue(processedError);
    
    // Show user notification if appropriate
    if (processedError.showToUser) {
      this.showErrorToUser(processedError);
    }
    
    // Report error if in production
    if (ENVIRONMENT.IS_PRODUCTION) {
      this.reportError(processedError);
    }
    
    return processedError;
  }

  /**
   * Process and normalize error information
   * @param {Object} errorInfo - Raw error information
   * @returns {Object} Processed error object
   */
  processError(errorInfo) {
    const error = {
      id: this.generateErrorId(),
      timestamp: new Date().toISOString(),
      type: errorInfo.type || ERROR_TYPES.UNKNOWN,
      message: errorInfo.message || 'Unknown error occurred',
      originalError: errorInfo.error,
      context: errorInfo.context || '',
      severity: errorInfo.severity || ERROR_SEVERITY.MEDIUM,
      userAgent: navigator.userAgent,
      url: window.location.href,
      showToUser: true
    };

    // Determine user-friendly message
    error.userMessage = this.getUserFriendlyMessage(error);
    
    // Determine if error should be shown to user
    error.showToUser = this.shouldShowToUser(error);
    
    return error;
  }

  /**
   * Get user-friendly error message
   * @param {Object} error - Processed error object
   * @returns {string} User-friendly message
   */
  getUserFriendlyMessage(error) {
    switch (error.type) {
      case ERROR_TYPES.NETWORK:
        return ERROR_MESSAGES.NETWORK;
      case ERROR_TYPES.TIMEOUT:
        return ERROR_MESSAGES.TIMEOUT;
      case ERROR_TYPES.FILE:
        if (error.message.includes('size')) {
          return ERROR_MESSAGES.FILE_TOO_LARGE;
        }
        if (error.message.includes('type')) {
          return ERROR_MESSAGES.INVALID_FILE_TYPE;
        }
        return error.message;
      case ERROR_TYPES.API:
        if (error.message.includes('401') || error.message.includes('authentication')) {
          return ERROR_MESSAGES.API_KEY_MISSING;
        }
        if (error.message.includes('500')) {
          return ERROR_MESSAGES.SERVER;
        }
        return ERROR_MESSAGES.GENERIC;
      default:
        return ERROR_MESSAGES.GENERIC;
    }
  }

  /**
   * Determine if error should be shown to user
   * @param {Object} error - Processed error object
   * @returns {boolean} Whether to show error to user
   */
  shouldShowToUser(error) {
    // Don't show low severity errors
    if (error.severity === ERROR_SEVERITY.LOW) {
      return false;
    }
    
    // Don't show repeated errors too frequently
    const recentSimilarErrors = this.errorQueue.filter(e => 
      e.type === error.type && 
      e.message === error.message &&
      Date.now() - new Date(e.timestamp).getTime() < 5000 // 5 seconds
    );
    
    return recentSimilarErrors.length === 0;
  }

  /**
   * Log error to console
   * @param {Object} error - Processed error object
   */
  logError(error) {
    const logLevel = this.getLogLevel(error.severity);
    const logMessage = `[${error.type.toUpperCase()}] ${error.message}`;
    
    console[logLevel](logMessage, {
      id: error.id,
      context: error.context,
      originalError: error.originalError,
      timestamp: error.timestamp
    });
  }

  /**
   * Get appropriate console log level
   * @param {string} severity - Error severity
   * @returns {string} Console method name
   */
  getLogLevel(severity) {
    switch (severity) {
      case ERROR_SEVERITY.LOW:
        return 'info';
      case ERROR_SEVERITY.MEDIUM:
        return 'warn';
      case ERROR_SEVERITY.HIGH:
      case ERROR_SEVERITY.CRITICAL:
        return 'error';
      default:
        return 'log';
    }
  }

  /**
   * Add error to queue
   * @param {Object} error - Processed error object
   */
  addToQueue(error) {
    this.errorQueue.push(error);
    
    // Maintain queue size
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift();
    }
  }

  /**
   * Show error notification to user
   * @param {Object} error - Processed error object
   */
  showErrorToUser(error) {
    // Create or update notification element
    this.showNotification(error.userMessage, 'error');
  }

  /**
   * Show notification to user
   * @param {string} message - Message to show
   * @param {string} type - Notification type (error, success, info)
   */
  showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.error-notification');
    existingNotifications.forEach(notification => notification.remove());

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `error-notification notification-${type}`;
    notification.textContent = message;
    
    // Add styles
    Object.assign(notification.style, {
      position: 'fixed',
      top: '20px',
      right: '20px',
      padding: '12px 20px',
      borderRadius: '8px',
      color: 'white',
      backgroundColor: type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#17a2b8',
      zIndex: '10000',
      maxWidth: '400px',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
      animation: 'slideInRight 0.3s ease-out'
    });

    // Add to DOM
    document.body.appendChild(notification);

    // Auto-remove after delay
    setTimeout(() => {
      if (notification.parentNode) {
        notification.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => notification.remove(), 300);
      }
    }, APP_CONFIG.UI.NOTIFICATION_DURATION);
  }

  /**
   * Report error to external service (in production)
   * @param {Object} error - Processed error object
   */
  reportError(error) {
    // Only report in production and for medium+ severity
    if (!ENVIRONMENT.IS_PRODUCTION || error.severity === ERROR_SEVERITY.LOW) {
      return;
    }

    // Here you would integrate with error reporting service
    // Example: Sentry, LogRocket, Bugsnag, etc.
    console.info('Error reported:', error.id);
  }

  /**
   * Generate unique error ID
   * @returns {string} Unique error ID
   */
  generateErrorId() {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get error statistics
   * @returns {Object} Error statistics
   */
  getErrorStats() {
    const now = Date.now();
    const last24Hours = this.errorQueue.filter(error => 
      now - new Date(error.timestamp).getTime() < 24 * 60 * 60 * 1000
    );

    return {
      total: this.errorQueue.length,
      last24Hours: last24Hours.length,
      byType: this.groupBy(last24Hours, 'type'),
      bySeverity: this.groupBy(last24Hours, 'severity')
    };
  }

  /**
   * Group array by property
   * @param {Array} array - Array to group
   * @param {string} property - Property to group by
   * @returns {Object} Grouped object
   */
  groupBy(array, property) {
    return array.reduce((groups, item) => {
      const key = item[property];
      groups[key] = (groups[key] || 0) + 1;
      return groups;
    }, {});
  }
}

// Create global error handler instance
export const errorHandler = new ErrorHandler();

// Convenience functions
export const handleError = (errorInfo) => errorHandler.handleError(errorInfo);
export const showNotification = (message, type) => errorHandler.showNotification(message, type);

export default errorHandler;
