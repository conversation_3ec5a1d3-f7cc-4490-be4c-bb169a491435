"""
Django settings module selector.

This module automatically loads the appropriate settings based on the
DJANGO_ENVIRONMENT environment variable.

Environment options:
- development (default)
- production
- testing
"""

import os

# Get the environment from environment variable
environment = os.getenv('DJANGO_ENVIRONMENT', 'development').lower()

# Import the appropriate settings module
if environment == 'production':
    from .production import *
elif environment == 'testing':
    from .testing import *
else:
    # Default to development
    from .development import *

# Set the environment variable for reference
ENVIRONMENT = environment
